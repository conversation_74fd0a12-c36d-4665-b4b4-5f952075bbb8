# 🚀 耀森网站部署指南

## 📋 部署概览

本项目采用**本地开发 + 远程生产部署**模式：
- **本地开发**：使用 npm run dev 进行本地开发（推荐）
- **生产部署**：使用自动化脚本部署到远程服务器
- **容器化测试**：使用 Docker 进行本地生产环境测试

## 🚀 快速开始

### 1. 服务器准备

#### 安装 Docker 和 Docker Compose
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 2. 上传项目文件

#### 方法一：使用 Git（推荐）
```bash
# 在服务器上克隆项目
git clone <your-repository-url>
cd yaosen-website
```

#### 方法二：使用 SCP 上传
```bash
# 在本地执行，上传整个项目
scp -r yaosen-website/ root@your-server-ip:/opt/
```

### 3. 部署网站

```bash
# 进入项目目录
cd /opt/yaosen-website

# 给部署脚本执行权限
chmod +x deploy.sh

# 部署生产环境
./deploy.sh prod
```

## 🛠️ 部署命令详解

### 基本命令
```bash
# 容器化开发测试（推荐直接使用 npm run dev）
./deploy.sh dev-test

# 生产环境
./deploy.sh prod

# 停止所有服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看日志
./deploy.sh logs

# 清理Docker资源
./deploy.sh cleanup
```

### 手动 Docker Compose 命令
```bash
# 容器化开发测试
docker-compose --profile dev up -d

# 生产环境
docker-compose --profile prod up -d
```



## 🌐 域名和SSL配置

### 1. 域名解析
将您的域名A记录指向服务器IP地址

### 2. 使用 Nginx 反向代理
```bash
# 启动带 Nginx 的生产环境
docker-compose --profile prod-nginx up -d
```

### 3. SSL证书配置
```bash
# 创建SSL证书目录
mkdir -p ssl

# 将证书文件放入ssl目录
# ssl/cert.pem - 证书文件
# ssl/key.pem - 私钥文件

# 修改 nginx.conf 中的HTTPS配置
# 取消注释HTTPS server块
```

## 📊 监控和维护

### 查看服务状态
```bash
# 查看容器状态
docker ps

# 查看服务日志
docker-compose logs -f

# 查看资源使用
docker stats
```

### 更新部署
```bash
# 拉取最新代码
git pull

# 重新构建并部署
./deploy.sh prod
```

### 备份数据
```bash
# 备份项目文件
tar -czf yaosen-backup-$(date +%Y%m%d).tar.gz /opt/yaosen-website
```

## 🔒 安全建议

1. **修改默认密码**：更改 code-server 的默认密码
2. **防火墙配置**：只开放必要端口（80, 443, 22）
3. **定期更新**：保持系统和Docker镜像更新
4. **SSL证书**：生产环境建议使用HTTPS

## 🐛 故障排除

### 常见问题

#### 端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep :80

# 停止占用端口的服务
sudo systemctl stop apache2  # 如果是Apache
sudo systemctl stop nginx    # 如果是Nginx
```

#### 容器启动失败
```bash
# 查看详细错误日志
docker-compose logs yaosen-prod

# 重新构建镜像
docker-compose build --no-cache
```

#### 文件权限问题
```bash
# 修复文件权限
sudo chown -R $USER:$USER /opt/yaosen-website
chmod +x deploy.sh
```

## 📞 技术支持

如遇到部署问题，请检查：
1. Docker 和 Docker Compose 版本
2. 服务器内存和磁盘空间
3. 网络连接和防火墙设置
4. 日志文件中的错误信息
