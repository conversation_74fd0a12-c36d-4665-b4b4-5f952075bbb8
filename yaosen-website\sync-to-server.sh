#!/bin/bash

# 本地开发文件同步到远程服务器脚本
# 使用方法: ./sync-to-server.sh [watch|once|setup]

set -e

# 配置变量 - 请根据您的服务器信息修改
SERVER_IP="${SERVER_IP:-your-server-ip}"
SERVER_USER="${SERVER_USER:-root}"
SERVER_PATH="${SERVER_PATH:-/opt/yaosen-website}"
LOCAL_PATH="."

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    if ! command -v rsync &> /dev/null; then
        log_error "rsync 未安装，请先安装 rsync"
        echo "Ubuntu/Debian: sudo apt install rsync"
        echo "macOS: brew install rsync"
        echo "Windows: 使用 WSL 或安装 Git Bash"
        exit 1
    fi
    
    if ! command -v ssh &> /dev/null; then
        log_error "ssh 未安装，请先安装 OpenSSH"
        exit 1
    fi
}

# 测试服务器连接
test_connection() {
    log_info "测试服务器连接..."

    # 安全的SSH连接选项
    local ssh_opts="-o ConnectTimeout=10 -o BatchMode=yes -o StrictHostKeyChecking=yes -o UserKnownHostsFile=~/.ssh/known_hosts"

    if ssh $ssh_opts $SERVER_USER@$SERVER_IP exit 2>/dev/null; then
        log_success "服务器连接正常"
        return 0
    else
        log_error "无法连接到服务器 $SERVER_USER@$SERVER_IP"
        log_warning "请检查："
        echo "  1. 服务器IP地址是否正确"
        echo "  2. SSH密钥是否已配置"
        echo "  3. 服务器是否允许SSH连接"
        echo "  4. 服务器主机密钥是否已添加到known_hosts"
        echo "     首次连接请运行: ssh-keyscan -H $SERVER_IP >> ~/.ssh/known_hosts"
        return 1
    fi
}

# 配置SSH密钥
setup_ssh() {
    log_info "配置SSH密钥..."
    
    if [ ! -f ~/.ssh/id_rsa ]; then
        log_info "生成SSH密钥..."
        ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
    fi
    
    log_info "复制公钥到服务器..."
    ssh-copy-id $SERVER_USER@$SERVER_IP
    
    if test_connection; then
        log_success "SSH密钥配置完成"
    else
        log_error "SSH密钥配置失败"
        exit 1
    fi
}

# 一次性同步
sync_once() {
    log_info "开始同步文件到服务器..."
    
    # 确保远程目录存在
    ssh $SERVER_USER@$SERVER_IP "mkdir -p $SERVER_PATH"
    
    # 同步文件，排除不需要的目录
    rsync -avz --progress \
        -e "ssh -o StrictHostKeyChecking=yes -o UserKnownHostsFile=~/.ssh/known_hosts" \
        --exclude 'node_modules' \
        --exclude '.next' \
        --exclude '.git' \
        --exclude 'dist' \
        --exclude '*.log' \
        --exclude '.env.local' \
        --exclude '.env' \
        --exclude '.DS_Store' \
        --exclude 'Thumbs.db' \
        --exclude 'ssl/*.key' \
        --exclude 'ssl/*.pem' \
        $LOCAL_PATH/ $SERVER_USER@$SERVER_IP:$SERVER_PATH/
    
    log_success "文件同步完成"
    
    # 重启远程开发服务器
    log_info "重启远程开发服务器..."
    ssh -o StrictHostKeyChecking=yes -o UserKnownHostsFile=~/.ssh/known_hosts $SERVER_USER@$SERVER_IP "cd $SERVER_PATH && docker-compose restart yaosen-prod 2>/dev/null || echo '容器未运行，跳过重启'"
}

# 监听文件变化并自动同步
watch_and_sync() {
    log_info "开始监听文件变化..."
    log_warning "按 Ctrl+C 停止监听"
    
    # 检查是否安装了文件监听工具
    if command -v fswatch &> /dev/null; then
        # macOS 使用 fswatch
        fswatch -o $LOCAL_PATH | while read f; do
            log_info "检测到文件变化，开始同步..."
            sync_once
        done
    elif command -v inotifywait &> /dev/null; then
        # Linux 使用 inotify
        while inotifywait -r -e modify,create,delete,move \
            --exclude '(node_modules|\.next|\.git|dist|.*\.log)' \
            $LOCAL_PATH; do
            log_info "检测到文件变化，开始同步..."
            sync_once
        done
    else
        log_warning "未找到文件监听工具，使用轮询模式..."
        log_info "安装建议："
        echo "  macOS: brew install fswatch"
        echo "  Ubuntu/Debian: sudo apt install inotify-tools"
        echo ""
        
        # 轮询模式
        LAST_SYNC=$(date +%s)
        while true; do
            CURRENT_TIME=$(date +%s)
            if [ $((CURRENT_TIME - LAST_SYNC)) -gt 5 ]; then
                if find $LOCAL_PATH -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.css" -newer /tmp/last_sync 2>/dev/null | grep -q .; then
                    log_info "检测到文件变化，开始同步..."
                    sync_once
                    touch /tmp/last_sync
                    LAST_SYNC=$CURRENT_TIME
                fi
            fi
            sleep 2
        done
    fi
}

# 显示配置信息
show_config() {
    echo -e "${BLUE}当前配置:${NC}"
    echo "  服务器地址: $SERVER_USER@$SERVER_IP"
    echo "  远程路径: $SERVER_PATH"
    echo "  本地路径: $LOCAL_PATH"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "本地开发文件同步脚本"
    echo ""
    echo "使用方法:"
    echo "  ./sync-to-server.sh setup   - 配置SSH密钥"
    echo "  ./sync-to-server.sh once    - 执行一次同步"
    echo "  ./sync-to-server.sh watch   - 监听文件变化并自动同步"
    echo "  ./sync-to-server.sh config  - 显示当前配置"
    echo "  ./sync-to-server.sh help    - 显示帮助信息"
    echo ""
    echo "首次使用请先运行: ./sync-to-server.sh setup"
}

# 主函数
main() {
    show_config
    check_dependencies
    
    case "${1:-help}" in
        "setup")
            setup_ssh
            ;;
        "once")
            if test_connection; then
                sync_once
            fi
            ;;
        "watch")
            if test_connection; then
                watch_and_sync
            fi
            ;;
        "config")
            show_config
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

main "$@"
