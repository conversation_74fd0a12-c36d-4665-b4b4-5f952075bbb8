#!/bin/bash

# 耀森网站一键部署脚本
# 适用于阿里云轻量服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}"
echo "=================================================="
echo "    耀森企业管理有限公司网站一键部署脚本"
echo "=================================================="
echo -e "${NC}"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用root用户运行此脚本${NC}"
    exit 1
fi

# 更新系统
echo -e "${YELLOW}[1/6] 更新系统...${NC}"
apt update && apt upgrade -y

# 安装Docker
echo -e "${YELLOW}[2/6] 安装Docker...${NC}"
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    systemctl start docker
    systemctl enable docker
    echo -e "${GREEN}Docker安装完成${NC}"
else
    echo -e "${GREEN}Docker已安装${NC}"
fi

# 安装Docker Compose
echo -e "${YELLOW}[3/6] 安装Docker Compose...${NC}"
if ! command -v docker-compose &> /dev/null; then
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    echo -e "${GREEN}Docker Compose安装完成${NC}"
else
    echo -e "${GREEN}Docker Compose已安装${NC}"
fi

# 创建项目目录
echo -e "${YELLOW}[4/6] 创建项目目录...${NC}"
mkdir -p /opt/yaosen-website
cd /opt/yaosen-website

# 设置防火墙
echo -e "${YELLOW}[5/6] 配置防火墙...${NC}"
if command -v ufw &> /dev/null; then
    ufw allow 22/tcp
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw allow 3000/tcp
    ufw allow 8080/tcp
    echo "y" | ufw enable
    echo -e "${GREEN}防火墙配置完成${NC}"
fi

# 部署网站
echo -e "${YELLOW}[6/6] 部署网站...${NC}"
if [ -f "deploy.sh" ]; then
    chmod +x deploy.sh
    ./deploy.sh prod
else
    echo -e "${RED}请先上传项目文件到 /opt/yaosen-website 目录${NC}"
    echo -e "${YELLOW}然后运行: ./deploy.sh prod${NC}"
fi

echo -e "${GREEN}"
echo "=================================================="
echo "              部署完成！"
echo "=================================================="
echo -e "${NC}"
echo -e "${BLUE}访问地址: http://$(curl -s ifconfig.me)${NC}"
echo -e "${BLUE}管理命令: cd /opt/yaosen-website && ./deploy.sh [dev|prod|stop|logs]${NC}"
echo ""