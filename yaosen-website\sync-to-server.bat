@echo off
REM Windows 本地开发文件同步到远程服务器脚本
REM 使用方法: sync-to-server.bat [once|watch|setup|config]

setlocal enabledelayedexpansion

REM 配置变量 - 请根据您的服务器信息修改
set SERVER_IP=your-server-ip
set SERVER_USER=root
set SERVER_PATH=/opt/yaosen-website
set LOCAL_PATH=%cd%

REM 颜色定义
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

echo %BLUE%=================================================%NC%
echo %BLUE%    耀森网站本地开发同步脚本 (Windows)%NC%
echo %BLUE%=================================================%NC%
echo.

REM 检查依赖
:check_dependencies
where rsync >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%NC% rsync 未安装
    echo 请安装以下工具之一:
    echo   1. Git for Windows ^(包含rsync^)
    echo   2. WSL ^(Windows Subsystem for Linux^)
    echo   3. Cygwin
    echo   4. MSYS2
    pause
    exit /b 1
)

where ssh >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%NC% SSH 未安装
    echo 请安装 OpenSSH 或 Git for Windows
    pause
    exit /b 1
)

REM 显示配置
:show_config
echo %BLUE%当前配置:%NC%
echo   服务器地址: %SERVER_USER%@%SERVER_IP%
echo   远程路径: %SERVER_PATH%
echo   本地路径: %LOCAL_PATH%
echo.

REM 测试连接
:test_connection
echo %BLUE%[INFO]%NC% 测试服务器连接...
ssh -o ConnectTimeout=5 -o BatchMode=yes %SERVER_USER%@%SERVER_IP% exit >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%NC% 无法连接到服务器 %SERVER_USER%@%SERVER_IP%
    echo 请检查:
    echo   1. 服务器IP地址是否正确
    echo   2. SSH密钥是否已配置
    echo   3. 服务器是否允许SSH连接
    if "%1"=="setup" goto setup_ssh
    pause
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% 服务器连接正常
goto main

REM 配置SSH密钥
:setup_ssh
echo %BLUE%[INFO]%NC% 配置SSH密钥...

if not exist "%USERPROFILE%\.ssh\id_rsa" (
    echo %BLUE%[INFO]%NC% 生成SSH密钥...
    ssh-keygen -t rsa -b 4096 -f "%USERPROFILE%\.ssh\id_rsa" -N ""
)

echo %BLUE%[INFO]%NC% 复制公钥到服务器...
ssh-copy-id %SERVER_USER%@%SERVER_IP%

echo %GREEN%[SUCCESS]%NC% SSH密钥配置完成
goto test_connection

REM 一次性同步
:sync_once
echo %BLUE%[INFO]%NC% 开始同步文件到服务器...

REM 确保远程目录存在
ssh %SERVER_USER%@%SERVER_IP% "mkdir -p %SERVER_PATH%"

REM 同步文件
rsync -avz --progress ^
    --exclude "node_modules" ^
    --exclude ".next" ^
    --exclude ".git" ^
    --exclude "dist" ^
    --exclude "*.log" ^
    --exclude ".env.local" ^
    --exclude ".DS_Store" ^
    --exclude "Thumbs.db" ^
    "%LOCAL_PATH%/" %SERVER_USER%@%SERVER_IP%:%SERVER_PATH%/

if %errorlevel% equ 0 (
    echo %GREEN%[SUCCESS]%NC% 文件同步完成
    
    REM 重启远程服务
    echo %BLUE%[INFO]%NC% 重启远程服务...
    ssh %SERVER_USER%@%SERVER_IP% "cd %SERVER_PATH% && docker-compose restart yaosen-prod 2>/dev/null || echo '容器未运行，跳过重启'"
) else (
    echo %RED%[ERROR]%NC% 文件同步失败
)
goto end

REM 监听模式（轮询）
:watch_mode
echo %BLUE%[INFO]%NC% 开始监听文件变化...
echo %YELLOW%[WARNING]%NC% 按 Ctrl+C 停止监听
echo.

REM 创建临时文件记录上次同步时间
echo %date% %time% > "%TEMP%\last_sync.txt"

:watch_loop
timeout /t 3 /nobreak >nul

REM 检查文件是否有变化（简单的时间戳检查）
for /r "%LOCAL_PATH%\src" %%f in (*.tsx *.ts *.js *.css) do (
    forfiles /p "%%~dpf" /m "%%~nxf" /d +0 /c "cmd /c echo %%~nxf changed" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %BLUE%[INFO]%NC% 检测到文件变化: %%~nxf
        call :sync_once
        echo %date% %time% > "%TEMP%\last_sync.txt"
        timeout /t 2 /nobreak >nul
    )
)

goto watch_loop

REM 显示帮助
:show_help
echo 本地开发文件同步脚本 ^(Windows^)
echo.
echo 使用方法:
echo   sync-to-server.bat setup   - 配置SSH密钥
echo   sync-to-server.bat once    - 执行一次同步
echo   sync-to-server.bat watch   - 监听文件变化并自动同步
echo   sync-to-server.bat config  - 显示当前配置
echo   sync-to-server.bat help    - 显示帮助信息
echo.
echo 首次使用请先运行: sync-to-server.bat setup
goto end

REM 主逻辑
:main
if "%1"=="setup" goto setup_ssh
if "%1"=="once" goto sync_once
if "%1"=="watch" goto watch_mode
if "%1"=="config" goto show_config
goto show_help

:end
echo.
pause
