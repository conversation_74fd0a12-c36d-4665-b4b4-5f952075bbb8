# 🚀 远程Docker服务器部署指南

## 📋 概述

本指南将帮助您将耀森网站项目部署到远程Docker服务器（Docker v26.1.3）。

## 🎯 部署方式选择

### 方式一：一键自动部署（推荐）

#### Linux/macOS 用户
```bash
cd yaosen-website
./deploy-to-remote.sh
```

#### Windows 用户
```cmd
cd yaosen-website
deploy-to-remote.bat
```

### 方式二：手动分步部署

适合需要更多控制的高级用户。

## 🛠️ 方式一：一键自动部署

### 前置要求

#### Linux/macOS
- SSH客户端
- rsync
- sshpass（如果使用密码认证）

```bash
# Ubuntu/Debian
sudo apt install openssh-client rsync sshpass

# macOS
brew install rsync sshpass
```

#### Windows
- Git for Windows（包含SSH和SCP工具）
- 或者安装OpenSSH

### 部署步骤

1. **运行部署脚本**
   ```bash
   # Linux/macOS
   ./deploy-to-remote.sh
   
   # Windows
   deploy-to-remote.bat
   ```

2. **输入服务器信息**
   - 服务器IP地址
   - SSH用户名（默认：root）
   - 选择认证方式（密码或SSH密钥）
   - 远程部署路径（默认：/opt/yaosen-website）

3. **等待自动部署完成**
   脚本将自动执行：
   - 测试服务器连接
   - 安装Docker环境
   - 上传项目文件
   - 构建并启动容器
   - 配置防火墙

4. **访问网站**
   部署完成后，通过显示的IP地址访问网站

## 🔧 方式二：手动分步部署

### 步骤1：准备服务器环境

```bash
# 连接到服务器
ssh root@your-server-ip

# 更新系统
apt update && apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl start docker
systemctl enable docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 步骤2：上传项目文件

#### 方法A：使用rsync（推荐）
```bash
# 在本地执行
rsync -avz --progress \
    --exclude 'node_modules' \
    --exclude '.next' \
    --exclude '.git' \
    --exclude 'dist' \
    --exclude '*.log' \
    yaosen-website/ root@your-server-ip:/opt/yaosen-website/
```

#### 方法B：使用scp
```bash
# 在本地执行
scp -r yaosen-website/ root@your-server-ip:/opt/
```

#### 方法C：使用Git
```bash
# 在服务器上执行
git clone <your-repository-url> /opt/yaosen-website
cd /opt/yaosen-website
```

### 步骤3：部署应用

```bash
# 在服务器上执行
cd /opt/yaosen-website

# 给脚本执行权限
chmod +x deploy.sh

# 部署生产环境
./deploy.sh prod
```

### 步骤4：配置防火墙

```bash
# Ubuntu/Debian (ufw)
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable

# CentOS/RHEL (firewalld)
firewall-cmd --permanent --add-port=22/tcp
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --reload
```

## 📊 部署后管理

### 常用命令

```bash
# 连接到服务器
ssh root@your-server-ip

# 进入项目目录
cd /opt/yaosen-website

# 查看服务状态
./deploy.sh logs

# 重启服务
./deploy.sh restart

# 停止服务
./deploy.sh stop

# 重新部署
./deploy.sh prod
```

### 查看容器状态

```bash
# 查看运行中的容器
docker ps

# 查看所有容器
docker ps -a

# 查看容器日志
docker logs yaosen-website-yaosen-prod-1

# 查看资源使用情况
docker stats
```

### 更新部署

```bash
# 方法1：重新运行部署脚本
./deploy-to-remote.sh

# 方法2：手动更新
rsync -avz --progress \
    --exclude 'node_modules' \
    --exclude '.next' \
    --exclude '.git' \
    yaosen-website/ root@your-server-ip:/opt/yaosen-website/

ssh root@your-server-ip "cd /opt/yaosen-website && ./deploy.sh prod"
```

## 🔒 安全建议

1. **使用SSH密钥认证**
   ```bash
   # 生成SSH密钥
   ssh-keygen -t rsa -b 4096
   
   # 复制公钥到服务器
   ssh-copy-id root@your-server-ip
   ```

2. **修改默认SSH端口**
   ```bash
   # 编辑SSH配置
   nano /etc/ssh/sshd_config
   
   # 修改端口
   Port 2222
   
   # 重启SSH服务
   systemctl restart sshd
   ```

3. **配置SSL证书**
   ```bash
   # 创建SSL目录
   mkdir -p /opt/yaosen-website/ssl
   
   # 上传证书文件
   # ssl/cert.pem - 证书文件
   # ssl/key.pem - 私钥文件
   
   # 启用HTTPS
   docker-compose --profile prod-nginx up -d
   ```

## 🐛 故障排除

### 常见问题

#### 1. 连接被拒绝
```bash
# 检查SSH服务状态
systemctl status sshd

# 检查防火墙设置
ufw status
```

#### 2. 端口被占用
```bash
# 查看端口占用
netstat -tlnp | grep :80

# 停止占用端口的服务
systemctl stop apache2
systemctl stop nginx
```

#### 3. 容器启动失败
```bash
# 查看详细日志
docker-compose logs yaosen-prod

# 重新构建镜像
docker-compose build --no-cache yaosen-prod
```

#### 4. 内存不足
```bash
# 查看内存使用
free -h

# 清理Docker资源
docker system prune -f
docker volume prune -f
```

## 📞 技术支持

如遇到部署问题，请检查：

1. **服务器要求**
   - 内存：至少1GB
   - 磁盘：至少5GB可用空间
   - 网络：能够访问Docker Hub

2. **网络连接**
   - SSH端口（默认22）是否开放
   - HTTP端口（80）是否开放
   - HTTPS端口（443）是否开放

3. **日志信息**
   ```bash
   # 查看部署日志
   ./deploy.sh logs
   
   # 查看系统日志
   journalctl -u docker
   ```

## 🎉 部署完成

部署成功后，您可以通过以下方式访问网站：

- **HTTP访问**: `http://your-server-ip`
- **管理界面**: SSH连接到服务器进行管理

恭喜！您的耀森网站已成功部署到Docker服务器！
