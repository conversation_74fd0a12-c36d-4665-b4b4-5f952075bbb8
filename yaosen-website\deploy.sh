#!/bin/bash

# 耀森网站部署脚本
# 使用方法: ./deploy.sh [dev|prod|stop|restart|logs]

set -e

PROJECT_NAME="yaosen-website"
COMPOSE_FILE="docker-compose.yml"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 开发环境部署
deploy_dev() {
    log_info "启动开发环境..."
    docker-compose --profile dev up --build -d
    log_success "开发环境已启动"
    log_info "访问地址: http://localhost:3000"
    log_info "查看日志: ./deploy.sh logs"
}

# 生产环境部署
deploy_prod() {
    log_info "部署生产环境..."
    
    # 停止现有容器
    docker-compose --profile prod down
    
    # 构建并启动
    docker-compose --profile prod up --build -d
    
    log_success "生产环境部署完成"
    log_info "访问地址: http://your-server-ip"
    log_info "查看日志: ./deploy.sh logs"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose --profile dev down
    docker-compose --profile prod down
    log_success "所有服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose restart
    log_success "服务已重启"
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f --tail=100
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    docker system prune -f
    docker volume prune -f
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "耀森网站部署脚本"
    echo ""
    echo "使用方法:"
    echo "  ./deploy.sh dev      - 启动开发环境"
    echo "  ./deploy.sh prod     - 部署生产环境"
    echo "  ./deploy.sh stop     - 停止所有服务"
    echo "  ./deploy.sh restart  - 重启服务"
    echo "  ./deploy.sh logs     - 查看日志"
    echo "  ./deploy.sh cleanup  - 清理Docker资源"
    echo "  ./deploy.sh help     - 显示帮助信息"
}

# 主函数
main() {
    check_docker
    
    case "${1:-help}" in
        "dev")
            deploy_dev
            ;;
        "prod")
            deploy_prod
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

main "$@"
