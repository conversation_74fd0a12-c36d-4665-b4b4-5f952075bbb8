#!/bin/bash

# 耀森网站Docker部署脚本 (官方镜像 + 目录映射)
# 使用方法: ./deploy.sh [prod|stop|restart|logs|dev-test]
# 新架构: 使用官方Node.js镜像，通过目录映射部署代码

set -e

PROJECT_NAME="yaosen-website"
COMPOSE_FILE="docker-compose.yml"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 本地开发环境测试（仅用于容器化测试）
deploy_dev_test() {
    log_warning "推荐直接使用 'npm run dev' 进行本地开发"
    log_info "启动容器化开发环境测试..."
    docker-compose --profile dev up --build -d
    log_success "开发测试环境已启动"
    log_info "访问地址: http://localhost:3000"
    log_info "查看日志: ./deploy.sh logs"
    log_info "停止测试: ./deploy.sh stop"
}

# 生产环境部署 (官方镜像 + 目录映射)
deploy_prod() {
    log_info "部署生产环境 (官方Node.js镜像 + 目录映射)..."

    # 停止现有容器
    docker-compose --profile prod down

    # 确保项目目录存在且有正确权限
    log_info "准备项目目录..."
    if [ ! -d "/opt/yaosen-website" ]; then
        log_info "创建项目目录 /opt/yaosen-website"
        sudo mkdir -p /opt/yaosen-website
        sudo chown -R $USER:$USER /opt/yaosen-website
    fi

    # 同步项目文件到映射目录
    log_info "同步项目文件..."
    rsync -av --delete \
        --exclude 'node_modules' \
        --exclude '.next' \
        --exclude '.git' \
        --exclude '*.log' \
        --exclude '.env.local' \
        ./ /opt/yaosen-website/

    # 启动生产环境 (不需要构建镜像)
    log_info "启动生产环境容器..."
    docker-compose --profile prod up -d

    # 等待容器启动
    sleep 10

    # 检查容器状态
    if docker-compose ps | grep -q "yaosen-prod.*Up"; then
        log_success "生产环境部署成功"
        log_info "访问地址: http://localhost"
        log_info "查看日志: ./deploy.sh logs"
        log_info "更新代码: 直接修改 /opt/yaosen-website 目录下的文件"
        log_info "重启应用: ./deploy.sh restart"
    else
        log_error "生产环境部署失败"
        log_info "查看错误日志: docker-compose logs yaosen-prod"
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose --profile dev down
    docker-compose --profile prod down
    log_success "所有服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose restart
    log_success "服务已重启"
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f --tail=100
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    docker system prune -f
    docker volume prune -f
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "耀森网站Docker部署脚本 (官方镜像 + 目录映射)"
    echo ""
    echo "🚀 新架构特点:"
    echo "  • 使用官方Node.js镜像，无需自定义构建"
    echo "  • 通过目录映射部署，更新代码无需重建镜像"
    echo "  • 更快的部署速度，更简单的维护"
    echo ""
    echo "📋 使用方法:"
    echo "  ./deploy.sh prod      - 部署生产环境 (映射到 /opt/yaosen-website)"
    echo "  ./deploy.sh dev-test  - 启动容器化开发测试"
    echo "  ./deploy.sh stop      - 停止所有服务"
    echo "  ./deploy.sh restart   - 重启服务"
    echo "  ./deploy.sh logs      - 查看日志"
    echo "  ./deploy.sh cleanup   - 清理Docker资源"
    echo ""
    echo "💻 本地开发推荐:"
    echo "  npm install          - 安装依赖"
    echo "  npm run dev          - 启动本地开发服务器"
    echo "  ./start-dev.sh       - 一键启动本地开发"
    echo ""
    echo "🔄 快速更新部署:"
    echo "  1. 修改本地代码"
    echo "  2. rsync 同步到 /opt/yaosen-website/"
    echo "  3. ./deploy.sh restart"
}

# 主函数
main() {
    check_docker
    
    case "${1:-help}" in
        "prod")
            deploy_prod
            ;;
        "dev-test")
            deploy_dev_test
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "cleanup")
            cleanup
            ;;
        "dev")
            log_warning "dev 命令已更改为 dev-test"
            log_info "推荐直接使用: npm run dev"
            log_info "或使用: ./deploy.sh dev-test"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

main "$@"
