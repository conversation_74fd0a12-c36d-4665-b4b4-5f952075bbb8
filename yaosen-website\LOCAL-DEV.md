# 本地开发 + 远程同步指南

## 🎯 概述

本方案让您在本地使用熟悉的开发环境，同时自动将代码同步到远程服务器，实现：
- 本地开发，享受快速的开发体验
- 自动同步到远程服务器
- 远程预览实际部署效果
- 支持多种同步模式

## 🚀 快速开始

### 1. 配置服务器信息

编辑同步脚本中的服务器配置：

```bash
# 编辑 sync-to-server.sh (Linux/macOS)
nano sync-to-server.sh

# 或编辑 sync-to-server.bat (Windows)
notepad sync-to-server.bat

# 修改以下配置
SERVER_IP="your-server-ip"          # 您的服务器IP
SERVER_USER="root"                  # SSH用户名
SERVER_PATH="/opt/yaosen-website"   # 远程项目路径
```

### 2. 配置SSH密钥（首次使用）

```bash
# Linux/macOS
./sync-to-server.sh setup

# Windows
sync-to-server.bat setup

# 或使用npm脚本
npm run sync:setup
```

### 3. 开始开发

#### 方法一：使用npm脚本（推荐）
```bash
# 同时启动本地开发服务器和文件监听
npm run dev:sync

# 或分别运行
npm run dev          # 启动本地开发服务器
npm run sync:watch   # 启动文件监听同步
```

#### 方法二：使用VS Code
1. 打开VS Code
2. 按 `Ctrl+Shift+P` 打开命令面板
3. 输入 "Tasks: Run Task"
4. 选择 "开始监听同步"

#### 方法三：手动执行
```bash
# Linux/macOS
./sync-to-server.sh watch

# Windows
sync-to-server.bat watch
```

## 🔄 同步模式

### 1. 一次性同步
```bash
# 立即同步一次
npm run sync:once

# 或直接使用脚本
./sync-to-server.sh once    # Linux/macOS
sync-to-server.bat once     # Windows
```

### 2. 监听模式
```bash
# 监听文件变化，自动同步
npm run sync:watch

# 或直接使用脚本
./sync-to-server.sh watch   # Linux/macOS
sync-to-server.bat watch    # Windows
```

### 3. 开发+同步模式
```bash
# 同时启动本地开发和自动同步
npm run dev:sync
```

## 🌐 访问方式

### 本地开发
- **地址**: `http://localhost:3000`
- **功能**: 快速开发，热重载

### 远程预览
- **地址**: `http://your-server-ip:3000` (开发环境)
- **地址**: `http://your-server-ip` (生产环境)
- **功能**: 查看在服务器上的实际效果

## 🛠️ 开发工作流

### 典型的开发流程

1. **启动开发环境**
   ```bash
   npm run dev:sync
   ```

2. **编辑代码**
   - 在本地编辑器中修改代码
   - 保存文件后自动同步到服务器

3. **预览效果**
   - 本地预览: `http://localhost:3000`
   - 远程预览: `http://your-server-ip:3000`

4. **部署生产**
   ```bash
   npm run deploy:prod
   ```

### VS Code 集成开发

#### 使用任务
- `Ctrl+Shift+P` → "Tasks: Run Task"
- 选择相应的任务：
  - "同步到服务器" - 一次性同步
  - "开始监听同步" - 启动自动同步
  - "本地开发服务器" - 启动本地开发
  - "部署到生产环境" - 部署到生产

#### 使用调试配置
- 按 `F5` 启动 "本地开发 + 自动同步"
- 或选择 "仅本地开发" 配置

## ⚙️ 高级配置

### 自定义同步规则

编辑 `sync-to-server.sh` 中的 rsync 参数：

```bash
rsync -avz --progress \
    --exclude 'node_modules' \
    --exclude '.next' \
    --exclude '.git' \
    --exclude 'dist' \
    --exclude '*.log' \
    --exclude '.env.local' \
    --exclude '.DS_Store' \
    --exclude 'Thumbs.db' \
    --include '*.tsx' \        # 只包含特定文件
    --include '*.ts' \
    --include '*.css' \
    $LOCAL_PATH/ $SERVER_USER@$SERVER_IP:$SERVER_PATH/
```

### 配置多个服务器

创建多个配置文件：

```bash
# 复制脚本
cp sync-to-server.sh sync-to-staging.sh
cp sync-to-server.sh sync-to-production.sh

# 修改各自的服务器配置
# staging服务器
SERVER_IP="staging-server-ip"

# 生产服务器
SERVER_IP="production-server-ip"
```

### 条件同步

只同步特定文件类型：

```bash
# 只同步源代码文件
rsync -avz --include="*.tsx" --include="*.ts" --include="*.css" --include="*.js" --exclude="*" ./src/ user@server:/path/src/
```

## 🔧 故障排除

### 常见问题

#### 1. SSH连接失败
```bash
# 检查SSH连接
ssh root@your-server-ip

# 重新配置SSH密钥
./sync-to-server.sh setup
```

#### 2. 文件监听不工作

**Linux/Ubuntu:**
```bash
# 安装inotify-tools
sudo apt install inotify-tools

# 增加文件监听限制
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

**macOS:**
```bash
# 安装fswatch
brew install fswatch
```

**Windows:**
```bash
# 使用Git Bash或WSL
# 或安装Windows版本的文件监听工具
```

#### 3. 同步速度慢
```bash
# 使用压缩传输
rsync -avz --compress-level=9 ...

# 只同步变化的文件
rsync -avz --checksum ...

# 排除大文件
rsync -avz --exclude="*.mp4" --exclude="*.zip" ...
```

#### 4. 权限问题
```bash
# 修复远程文件权限
ssh root@your-server-ip "chown -R www-data:www-data /opt/yaosen-website"

# 修复本地文件权限
chmod +x sync-to-server.sh
```

### 调试模式

启用详细输出：

```bash
# 在脚本中添加调试选项
rsync -avz --progress --verbose --dry-run ...  # 预览模式
rsync -avz --progress --verbose ...            # 详细输出
```

## 📊 性能优化

### 1. 增量同步
```bash
# 使用增量同步，只传输变化的部分
rsync -avz --partial --progress ...
```

### 2. 并行同步
```bash
# 使用多线程同步（需要安装parallel）
find ./src -name "*.tsx" | parallel -j4 rsync -avz {} user@server:/path/src/
```

### 3. 本地缓存
```bash
# 使用本地缓存目录
rsync -avz --backup --backup-dir=/tmp/backup ...
```

## 🔒 安全建议

1. **使用SSH密钥**: 避免密码认证
2. **限制SSH访问**: 配置防火墙规则
3. **定期更新**: 保持系统和工具更新
4. **备份重要文件**: 定期备份项目文件

## 📞 技术支持

### 有用的命令

```bash
# 查看同步状态
ps aux | grep rsync

# 停止所有同步进程
pkill -f "sync-to-server"

# 查看网络连接
netstat -an | grep :22

# 测试SSH连接速度
time ssh root@your-server-ip "echo 'connection test'"

# 查看磁盘使用
df -h
du -sh /opt/yaosen-website
```

### 监控脚本

```bash
# 创建监控脚本
cat > monitor-sync.sh << 'EOF'
#!/bin/bash
while true; do
    echo "=== $(date) ==="
    echo "本地文件数: $(find ./src -type f | wc -l)"
    echo "远程文件数: $(ssh root@your-server-ip 'find /opt/yaosen-website/src -type f | wc -l')"
    echo "同步进程: $(ps aux | grep -c sync-to-server)"
    echo ""
    sleep 30
done
EOF

chmod +x monitor-sync.sh
./monitor-sync.sh
```
