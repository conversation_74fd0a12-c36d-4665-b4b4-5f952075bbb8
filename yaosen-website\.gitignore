# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# 安全相关文件
ssl/*.key
ssl/*.pem
ssl/*.crt
*.backup
secrets/
credentials/
security-config.json

# SSH密钥
*.key
*.pub
!deploy_key.pub.example

# 日志文件
*.log
logs/

# 备份文件
*.backup.*
backup/

# 临时文件
tmp/
temp/
