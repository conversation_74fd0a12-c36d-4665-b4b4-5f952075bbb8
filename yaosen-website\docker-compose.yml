version: '3.8'

services:
  # 本地开发环境（仅用于容器化测试，推荐直接使用 npm run dev）
  yaosen-dev:
    image: node:18-alpine
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    command: sh -c "npm install && npm run dev"
    profiles:
      - dev

  # 生产环境 - 使用官方镜像 + 目录映射
  yaosen-prod:
    image: node:18-alpine
    working_dir: /app
    ports:
      - "80:3000"
    volumes:
      - /opt/yaosen-website:/app
      - /opt/yaosen-website/node_modules:/app/node_modules
    environment:
      - NODE_ENV=production
      - PORT=3000
    command: sh -c "npm ci --only=production && npm run build && npm start"
    restart: unless-stopped
    profiles:
      - prod
    # 安全配置
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "1000:1000"

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - yaosen-prod
    restart: unless-stopped
    profiles:
      - prod-nginx

networks:
  default:
    name: yaosen-network
