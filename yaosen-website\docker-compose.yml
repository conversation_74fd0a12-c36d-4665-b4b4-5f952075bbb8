version: '3.8'

services:
  # 本地开发环境（仅用于容器化测试，推荐直接使用 npm run dev）
  yaosen-dev:
    build:
      context: .
      target: dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
    command: npm run dev
    profiles:
      - dev

  # 生产环境
  yaosen-prod:
    build:
      context: .
      target: production
    ports:
      - "80:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    profiles:
      - prod
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/cache/nginx
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "1001:1001"

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - yaosen-prod
    restart: unless-stopped
    profiles:
      - prod-nginx

networks:
  default:
    name: yaosen-network
