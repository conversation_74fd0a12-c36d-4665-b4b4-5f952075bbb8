#!/bin/bash

# 同步配置向导脚本
# 帮助用户快速配置本地开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}"
echo "=================================================="
echo "    耀森网站本地开发环境配置向导"
echo "=================================================="
echo -e "${NC}"

# 收集用户输入
echo -e "${YELLOW}请输入您的服务器信息:${NC}"
echo ""

read -p "服务器IP地址: " SERVER_IP
read -p "SSH用户名 [root]: " SERVER_USER
SERVER_USER=${SERVER_USER:-root}
read -p "远程项目路径 [/opt/yaosen-website]: " SERVER_PATH
SERVER_PATH=${SERVER_PATH:-/opt/yaosen-website}

echo ""
echo -e "${BLUE}配置信息确认:${NC}"
echo "  服务器地址: $SERVER_USER@$SERVER_IP"
echo "  远程路径: $SERVER_PATH"
echo ""

read -p "确认配置正确吗? (y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "配置已取消"
    exit 1
fi

# 更新同步脚本配置
echo -e "${YELLOW}更新同步脚本配置...${NC}"

# 更新 Linux/macOS 脚本
if [ -f "sync-to-server.sh" ]; then
    sed -i.bak "s/SERVER_IP=\".*\"/SERVER_IP=\"$SERVER_IP\"/" sync-to-server.sh
    sed -i.bak "s/SERVER_USER=\".*\"/SERVER_USER=\"$SERVER_USER\"/" sync-to-server.sh
    sed -i.bak "s|SERVER_PATH=\".*\"|SERVER_PATH=\"$SERVER_PATH\"|" sync-to-server.sh
    rm sync-to-server.sh.bak
    echo -e "${GREEN}✓ sync-to-server.sh 已更新${NC}"
fi

# 更新 Windows 脚本
if [ -f "sync-to-server.bat" ]; then
    sed -i.bak "s/set SERVER_IP=.*/set SERVER_IP=$SERVER_IP/" sync-to-server.bat
    sed -i.bak "s/set SERVER_USER=.*/set SERVER_USER=$SERVER_USER/" sync-to-server.bat
    sed -i.bak "s|set SERVER_PATH=.*|set SERVER_PATH=$SERVER_PATH|" sync-to-server.bat
    rm sync-to-server.bat.bak
    echo -e "${GREEN}✓ sync-to-server.bat 已更新${NC}"
fi

# 更新 VS Code 任务配置
if [ -f ".vscode/tasks.json" ]; then
    sed -i.bak "s/root@your-server-ip/$SERVER_USER@$SERVER_IP/g" .vscode/tasks.json
    rm .vscode/tasks.json.bak
    echo -e "${GREEN}✓ VS Code 任务配置已更新${NC}"
fi

# 更新 package.json
if [ -f "package.json" ]; then
    sed -i.bak "s/root@your-server-ip/$SERVER_USER@$SERVER_IP/g" package.json
    rm package.json.bak
    echo -e "${GREEN}✓ package.json 已更新${NC}"
fi

# 更新 VS Code 设置
if [ -f ".vscode/settings.json" ]; then
    sed -i.bak "s/your-server-ip/$SERVER_IP/g" .vscode/settings.json
    rm .vscode/settings.json.bak
    echo -e "${GREEN}✓ VS Code 设置已更新${NC}"
fi

# 设置脚本权限
chmod +x sync-to-server.sh
chmod +x deploy.sh
chmod +x quick-deploy.sh

echo ""
echo -e "${GREEN}配置完成！${NC}"
echo ""
echo -e "${BLUE}下一步操作:${NC}"
echo "1. 配置SSH密钥: ./sync-to-server.sh setup"
echo "2. 测试连接: ./sync-to-server.sh once"
echo "3. 开始开发: npm run dev:sync"
echo ""
echo -e "${YELLOW}常用命令:${NC}"
echo "  npm run dev:sync     - 本地开发 + 自动同步"
echo "  npm run sync:once    - 一次性同步"
echo "  npm run sync:watch   - 监听文件变化"
echo "  npm run deploy:prod  - 部署到生产环境"
echo ""
