# 远程开发环境配置指南

## 🎯 概述

本指南将帮助您在阿里云轻量服务器上设置远程开发环境，实现：
- 在服务器上运行开发环境
- 通过浏览器进行代码编辑
- 实时预览网站效果
- 支持热重载和调试

## 🚀 快速开始

### 1. 服务器环境准备

```bash
# 连接到您的阿里云服务器
ssh root@your-server-ip

# 运行一键部署脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/yaosen-website/main/quick-deploy.sh | bash
```

### 2. 上传项目文件

#### 方法一：使用Git（推荐）
```bash
# 在服务器上
cd /opt
git clone https://github.com/your-username/yaosen-website.git
cd yaosen-website
```

#### 方法二：使用SCP上传
```bash
# 在本地电脑上执行
scp -r yaosen-website/ root@your-server-ip:/opt/
```

### 3. 启动远程开发环境

```bash
# 在服务器上
cd /opt/yaosen-website
chmod +x deploy.sh

# 启动远程开发环境
docker-compose -f docker-compose.dev.yml up -d
```

## 🌐 访问方式

### 网站预览
- **地址**: `http://your-server-ip:3000`
- **功能**: 实时预览网站效果，支持热重载

### 在线代码编辑器
- **地址**: `http://your-server-ip:8080`
- **用户名**: 无需用户名
- **密码**: 请在部署时设置强密码（见环境变量配置）

⚠️ **安全提醒**:
- 必须使用强密码（至少12位，包含大小写字母、数字和特殊字符）
- 建议使用密码生成器：`openssl rand -base64 32`
- 定期更换密码（建议每3个月）

## 🛠️ 开发工作流

### 1. 代码编辑
1. 打开浏览器访问 `http://your-server-ip:8080`
2. 输入密码登录在线编辑器
3. 在左侧文件树中选择要编辑的文件
4. 进行代码修改

### 2. 实时预览
1. 保存文件后，切换到预览标签页
2. 访问 `http://your-server-ip:3000` 查看效果
3. 页面会自动热重载显示最新修改

### 3. 调试功能
```bash
# 查看开发服务器日志
docker-compose -f docker-compose.dev.yml logs -f yaosen-remote-dev

# 进入容器进行调试
docker-compose -f docker-compose.dev.yml exec yaosen-remote-dev sh
```

## 🔧 高级配置

### 修改编辑器密码
```bash
# 编辑docker-compose.dev.yml文件
nano docker-compose.dev.yml

# 修改PASSWORD环境变量
environment:
  - PASSWORD=your-new-password

# 重启服务
docker-compose -f docker-compose.dev.yml restart code-server
```

### 配置域名访问
```bash
# 如果您有域名，可以配置Nginx反向代理
# 编辑nginx配置
nano /etc/nginx/sites-available/yaosen-dev

# 添加以下配置
server {
    listen 80;
    server_name dev.your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /code/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection upgrade;
    }
}

# 启用配置
ln -s /etc/nginx/sites-available/yaosen-dev /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx
```

### 文件同步配置
```bash
# 使用rsync同步本地文件到服务器
rsync -avz --exclude node_modules --exclude .next ./ root@your-server-ip:/opt/yaosen-website/

# 设置自动同步脚本
cat > sync.sh << 'EOF'
#!/bin/bash
while inotifywait -r -e modify,create,delete,move ./src; do
    rsync -avz --exclude node_modules --exclude .next ./ root@your-server-ip:/opt/yaosen-website/
done
EOF

chmod +x sync.sh
```

## 📱 移动端开发

### 移动端预览
```bash
# 确保防火墙允许3000端口
ufw allow 3000/tcp

# 在手机浏览器中访问
# http://your-server-ip:3000
```

### 响应式调试
1. 在code-server中打开浏览器开发者工具
2. 使用设备模拟器测试不同屏幕尺寸
3. 实时调整CSS样式

## 🔒 安全配置

### 1. 修改默认密码
```bash
# 生成强密码
openssl rand -base64 32

# 更新docker-compose.dev.yml中的密码
```

### 2. 配置SSL证书
```bash
# 使用Let's Encrypt获取免费SSL证书
apt install certbot
certbot --nginx -d dev.your-domain.com
```

### 3. 限制访问IP
```bash
# 只允许特定IP访问开发环境
ufw allow from your-office-ip to any port 3000
ufw allow from your-office-ip to any port 8080
```

## 📊 性能优化

### 1. 增加服务器资源
```bash
# 查看资源使用情况
docker stats
htop

# 如果内存不足，可以增加swap
fallocate -l 2G /swapfile
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile
echo '/swapfile none swap sw 0 0' >> /etc/fstab
```

### 2. 优化Docker配置
```bash
# 清理不用的Docker资源
docker system prune -f
docker volume prune -f

# 限制容器资源使用
# 在docker-compose.dev.yml中添加
deploy:
  resources:
    limits:
      memory: 1G
      cpus: '0.5'
```

## 🐛 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -tlnp | grep :3000
netstat -tlnp | grep :8080

# 停止占用端口的进程
kill -9 <PID>
```

#### 2. 文件权限问题
```bash
# 修复文件权限
chown -R 1001:1001 /opt/yaosen-website
chmod -R 755 /opt/yaosen-website
```

#### 3. 热重载不工作
```bash
# 确保环境变量设置正确
echo "WATCHPACK_POLLING=true" >> .env.local
echo "CHOKIDAR_USEPOLLING=true" >> .env.local

# 重启开发容器
docker-compose -f docker-compose.dev.yml restart
```

#### 4. 代码编辑器无法访问
```bash
# 检查容器状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs code-server

# 重启服务
docker-compose -f docker-compose.dev.yml restart code-server
```

## 📞 技术支持

### 有用的命令
```bash
# 查看所有容器状态
docker ps -a

# 查看容器日志
docker logs <container-name>

# 进入容器
docker exec -it <container-name> sh

# 重启所有服务
docker-compose -f docker-compose.dev.yml restart

# 完全重建
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml up --build -d
```

### 监控脚本
```bash
# 创建监控脚本
cat > monitor.sh << 'EOF'
#!/bin/bash
while true; do
    echo "=== $(date) ==="
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    sleep 30
done
EOF

chmod +x monitor.sh
./monitor.sh
```
