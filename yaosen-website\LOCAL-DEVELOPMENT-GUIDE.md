# 💻 耀森网站本地开发指南

## 🎯 开发模式说明

本项目采用**纯本地开发模式**，提供更好的安全性和开发体验。

## 📋 环境要求

### 必需软件
- **Node.js**: 18.0.0 或更高版本
- **npm**: 9.0.0 或更高版本
- **Git**: 最新版本

### 推荐软件
- **VS Code**: 推荐的代码编辑器
- **Docker**: 用于生产环境测试（可选）

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd yaosen-website
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
```bash
# 运行安全初始化脚本
./scripts/security-setup.sh

# 或手动复制环境变量模板
cp .env.example .env.local
```

### 4. 启动开发服务器
```bash
npm run dev
```

### 5. 访问应用
打开浏览器访问: http://localhost:3000

## 📝 开发命令

### 基础命令
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 运行类型检查
npm run type-check

# 运行代码检查
npm run lint

# 修复代码格式
npm run lint:fix
```

### 测试命令
```bash
# 运行单元测试
npm run test

# 运行测试并监听文件变化
npm run test:watch

# 运行测试覆盖率
npm run test:coverage

# 运行端到端测试
npm run test:e2e
```

### 构建和部署
```bash
# 构建生产版本
npm run build

# 分析构建包大小
npm run analyze

# 导出静态文件
npm run export
```

## 🛠️ 开发工具配置

### VS Code 推荐扩展
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

### VS Code 设置
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  }
}
```

## 📁 项目结构

```
yaosen-website/
├── src/                    # 源代码目录
│   ├── app/               # Next.js 13+ App Router
│   ├── components/        # React 组件
│   ├── lib/              # 工具函数和配置
│   ├── styles/           # 样式文件
│   └── types/            # TypeScript 类型定义
├── public/               # 静态资源
├── scripts/              # 构建和部署脚本
├── ssl/                  # SSL证书（本地开发用）
├── .env.example          # 环境变量模板
├── .env.local           # 本地环境变量（不提交）
├── next.config.js       # Next.js 配置
├── tailwind.config.js   # Tailwind CSS 配置
├── tsconfig.json        # TypeScript 配置
└── package.json         # 项目依赖和脚本
```

## 🎨 开发规范

### 代码风格
- 使用 **TypeScript** 进行类型检查
- 使用 **ESLint** 进行代码检查
- 使用 **Prettier** 进行代码格式化
- 使用 **Tailwind CSS** 进行样式开发

### 组件规范
```typescript
// 组件文件命名: PascalCase
// 文件名: UserProfile.tsx

import React from 'react';

interface UserProfileProps {
  name: string;
  email: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({ name, email }) => {
  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-xl font-bold">{name}</h2>
      <p className="text-gray-600">{email}</p>
    </div>
  );
};

export default UserProfile;
```

### 文件命名规范
- **组件文件**: PascalCase (UserProfile.tsx)
- **页面文件**: kebab-case (user-profile.tsx)
- **工具文件**: camelCase (formatDate.ts)
- **常量文件**: UPPER_SNAKE_CASE (API_ENDPOINTS.ts)

## 🔧 环境配置

### 开发环境变量
```bash
# .env.local
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=耀森企业管理有限公司
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# 开发专用配置
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_MOCK_API=true
```

### 生产环境变量
```bash
# .env.production
NODE_ENV=production
NEXT_PUBLIC_APP_NAME=耀森企业管理有限公司
NEXT_PUBLIC_SITE_URL=https://www.yaosen.com

# 生产专用配置
NEXT_PUBLIC_DEBUG=false
NEXT_PUBLIC_MOCK_API=false
```

## 🧪 测试指南

### 单元测试
```typescript
// __tests__/components/UserProfile.test.tsx
import { render, screen } from '@testing-library/react';
import UserProfile from '../src/components/UserProfile';

describe('UserProfile', () => {
  it('renders user information correctly', () => {
    render(<UserProfile name="张三" email="<EMAIL>" />);
    
    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
});
```

### 集成测试
```typescript
// __tests__/pages/index.test.tsx
import { render, screen } from '@testing-library/react';
import Home from '../src/app/page';

describe('Home Page', () => {
  it('renders homepage content', () => {
    render(<Home />);
    
    expect(screen.getByText('耀森企业管理有限公司')).toBeInTheDocument();
  });
});
```

## 🚀 部署流程

### 本地构建测试
```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 访问 http://localhost:3000 测试
```

### 容器化测试（可选）
```bash
# 构建并启动容器化开发测试
./deploy.sh dev-test

# 访问 http://localhost:3000 测试

# 停止测试
./deploy.sh stop
```

### 生产部署
```bash
# 运行安全检查
./security-check.sh

# 部署到生产服务器
./deploy-to-remote.sh

# 或使用同步脚本
./sync-to-server.sh once
```

## 🔍 调试指南

### 开发工具
- **React Developer Tools**: 浏览器扩展
- **Next.js DevTools**: 内置开发工具
- **VS Code Debugger**: 断点调试

### 常见问题

#### 1. 端口被占用
```bash
# 查找占用端口的进程
lsof -ti:3000

# 杀死进程
kill -9 <PID>

# 或使用不同端口
npm run dev -- -p 3001
```

#### 2. 依赖安装失败
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 3. 类型错误
```bash
# 运行类型检查
npm run type-check

# 重启 TypeScript 服务器（VS Code）
Ctrl+Shift+P -> "TypeScript: Restart TS Server"
```

## 📚 学习资源

### 技术文档
- [Next.js 官方文档](https://nextjs.org/docs)
- [React 官方文档](https://react.dev/)
- [TypeScript 官方文档](https://www.typescriptlang.org/docs/)
- [Tailwind CSS 官方文档](https://tailwindcss.com/docs)

### 最佳实践
- [Next.js 最佳实践](https://nextjs.org/docs/pages/building-your-application/deploying/production-checklist)
- [React 最佳实践](https://react.dev/learn/thinking-in-react)
- [TypeScript 最佳实践](https://typescript-eslint.io/docs/)

## 🤝 贡献指南

### 提交规范
```bash
# 功能开发
git commit -m "feat(组件): 添加用户资料组件"

# 问题修复
git commit -m "fix(样式): 修复移动端布局问题"

# 文档更新
git commit -m "docs(README): 更新安装说明"
```

### 分支管理
- `main`: 主分支，用于生产部署
- `develop`: 开发分支，用于功能集成
- `feature/*`: 功能分支，用于新功能开发
- `bugfix/*`: 修复分支，用于问题修复

---

**享受本地开发的高效体验！** 🎉
